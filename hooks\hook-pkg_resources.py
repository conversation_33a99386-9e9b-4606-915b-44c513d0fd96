#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller hook for pkg_resources package
解决pkg_resources相关模块缺失问题
"""

from PyInstaller.utils.hooks import collect_all, collect_submodules

# 收集pkg_resources包的所有模块和数据
datas, binaries, hiddenimports = collect_all('pkg_resources')

# 确保包含关键的pkg_resources子模块
hiddenimports += collect_submodules('pkg_resources')
hiddenimports += [
    'pkg_resources._vendor',
    'pkg_resources._vendor.packaging',
    'pkg_resources._vendor.packaging.version',
    'pkg_resources._vendor.packaging.specifiers',
    'pkg_resources._vendor.packaging.requirements',
    'pkg_resources.extern',
    'pkg_resources.py2_warn',
]
