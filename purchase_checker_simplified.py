#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
采购部绩效数据拉取与汇总系统
功能：从本地Excel文件读取所有数据，写入钉钉表格汇总
"""

import json
import pandas as pd
import os
import argparse
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dingtalk_sheet_utils import DingTalkSheetUtils
import calendar


class ResultWriter:
    """结果写入器 - 负责将数据写入钉钉表格"""
    
    def __init__(self, config_path: str = "config.json"):
        """初始化结果写入器"""
        self.config_path = config_path
        self.config = self._load_config()
        
        # 获取写入配置
        writer_config = self.config.get('result_writer', {})
        self.target_sheet_id = writer_config.get('target_sheet_id')
        self.result_sheet_name = writer_config.get('result_sheet_name', '采购部绩效明细')
        
        # 获取钉钉配置
        current_env = self.config.get('dingtalk', {}).get('current_environment', 'test')
        
        # 创建钉钉表格工具实例
        dingtalk_config = {
            'dingtalk': {
                'current_environment': current_env,
                'sheet': {
                    'app_key': self.config.get('dingtalk', {}).get('sheet', {}).get('app_key'),
                    'app_secret': self.config.get('dingtalk', {}).get('sheet', {}).get('app_secret'),
                    'operator_id': self.config.get('dingtalk', {}).get('sheet', {}).get('operator_id'),
                    'test_sheet': {
                        'sheet_id': self.target_sheet_id,
                        'sheet_name': '采购部绩效明细',
                        'sheet2_name': '采购部绩效汇总',
                        'last_row': 1
                    },
                    'prod_sheet': {
                        'sheet_id': self.target_sheet_id,
                        'sheet_name': '采购部绩效明细',
                        'sheet2_name': '采购部绩效汇总',
                        'last_row': 1
                    }
                }
            }
        }
        
        self.dingtalk_sheet = DingTalkSheetUtils(dingtalk_config)

        # 获取正确的工作表ID
        self.actual_sheet_id = self._get_sheet_id_by_name(self.result_sheet_name)
        if not self.actual_sheet_id:
            print(f"❌ 未找到工作表: {self.result_sheet_name}")
            self.actual_sheet_id = self.target_sheet_id  # 使用默认值

        print(f"✅ 结果写入器初始化完成，目标表格ID: {self.target_sheet_id}")
        print(f"📄 实际工作表ID: {self.actual_sheet_id}")

    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {str(e)}")
            return {}

    def _get_sheet_id_by_name(self, sheet_name: str) -> Optional[str]:
        """根据工作表名称获取工作表ID"""
        try:
            sheets = self.dingtalk_sheet.get_sheets_list()
            for sheet in sheets:
                if sheet.get('name') == sheet_name:
                    return sheet.get('id')
            print(f"⚠️ 未找到名为 '{sheet_name}' 的工作表")
            return None
        except Exception as e:
            print(f"❌ 获取工作表ID失败: {str(e)}")
            return None

    def get_current_month_info(self, target_date: str = None) -> Tuple[str, str, str]:
        """获取指定日期或前一天的月份信息"""
        if target_date:
            # 使用指定的日期
            try:
                target_dt = datetime.strptime(target_date, '%Y-%m-%d')
                print(f"📅 使用指定日期: {target_date}")
            except ValueError:
                print(f"❌ 日期格式错误: {target_date}，使用前一天日期")
                target_dt = datetime.now() - timedelta(days=1)
        else:
            # 默认使用前一天的日期
            target_dt = datetime.now() - timedelta(days=1)
            print(f"📅 使用前一天的数据: {target_dt.strftime('%Y-%m-%d')}")

        year = target_dt.year
        month = target_dt.month
        day = target_dt.day

        month_cn = f"{month}月"
        current_date_cn = f"{month}月{day}日"

        print(f"📅 处理日期: {target_dt.strftime('%Y-%m-%d')} ({current_date_cn})")
        return str(year), month_cn, current_date_cn

    def _find_monthly_table(self, sheet_id: str, table_title: str) -> Optional[int]:
        """动态查找月度汇总表的标题行号"""
        try:
            # 扩大搜索范围，支持更大的表格
            range_to_scan = "A1:A500"
            print(f"🔍 在工作表中动态扫描标题'{table_title}' (范围:{range_to_scan})...")

            column_data = self.dingtalk_sheet.get_cell_range(sheet_id, range_to_scan)
            if not column_data:
                print(f"⚠️ 无法读取工作表数据")
                return None

            print(f"📊 读取到 {len(column_data)} 行数据，开始查找标题...")

            # 记录找到的所有标题，用于调试
            found_titles = []

            for i, row in enumerate(column_data):
                if row and len(row) > 0:
                    cell_value = str(row[0]).strip()
                    if cell_value:  # 只记录非空值
                        found_titles.append(cell_value)

                        # 多种匹配方式
                        if (table_title in cell_value or
                            cell_value in table_title or
                            table_title == cell_value):
                            title_row_index = i + 1
                            print(f"✅ 找到匹配标题 '{cell_value}' 在第 {title_row_index} 行")
                            return title_row_index

            # 如果没找到，显示调试信息
            print(f"📋 工作表中找到的标题: {[t for t in found_titles if '数据汇总' in t or '月' in t]}")
            print(f"🎯 目标标题: '{table_title}'")
            print(f"🤷‍♀️ 未找到匹配的月度汇总表标题")
            return None

        except Exception as e:
            print(f"❌ 查找月度汇总表失败: {e}")
            return None

    def _find_date_row(self, sheet_id: str, target_date: str, start_row: int) -> Optional[int]:
        """动态查找指定日期对应的行号 - 支持多种日期格式匹配"""
        try:
            print(f"🔍 动态查找日期 '{target_date}' (从第 {start_row} 行开始)...")

            # 首先扩大搜索范围，从start_row开始搜索更大的范围
            max_search_rows = 200  # 扩大搜索范围
            search_range = f"A{start_row}:A{start_row + max_search_rows}"

            column_data = self.dingtalk_sheet.get_cell_range(sheet_id, search_range)
            if not column_data:
                print(f"⚠️ 无法读取搜索范围 {search_range} 的数据")
                return None

            print(f"📊 读取到 {len(column_data)} 行数据，开始逐行匹配...")

            # 生成多种可能的日期格式进行匹配
            target_formats = self._generate_date_formats(target_date)
            print(f"🎯 生成的匹配格式: {target_formats}")

            # 记录找到的所有日期，用于调试
            found_dates = []

            for i, row in enumerate(column_data):
                if row and len(row) > 0:
                    cell_value = str(row[0]).strip()
                    if cell_value:  # 只记录非空值
                        found_dates.append(cell_value)

                        # 使用智能匹配逻辑
                        if self._is_date_match(cell_value, target_formats):
                            found_row = start_row + i
                            print(f"✅ 找到匹配日期 '{cell_value}' 在第 {found_row} 行")
                            return found_row

            # 如果没找到，显示调试信息
            print(f"📅 搜索范围内找到的所有日期: {found_dates[:10]}...")  # 只显示前10个
            print(f"🎯 目标日期: '{target_date}'")
            print(f"⚠️ 在第 {start_row}-{start_row + len(column_data)} 行范围内未找到匹配的日期")

            # 不再自动创建缺失的日期行，而是返回None让调用者处理
            print(f"💡 提示：表格中的日期格式可能与目标格式不匹配")
            return None

        except Exception as e:
            print(f"❌ 查找日期行失败: {str(e)}")
            return None

    def _generate_date_formats(self, target_date: str) -> List[str]:
        """
        根据目标日期生成多种可能的格式
        例如：7月24日 -> ['7月24日', '2025/7/24', '2025-7-24', '7/24', '07/24']
        """
        import re
        from datetime import datetime

        formats = [target_date]  # 原始格式

        # 如果是中文格式（如：7月24日），转换为其他格式
        if '月' in target_date and '日' in target_date:
            # 提取月和日
            match = re.search(r'(\d+)月(\d+)日', target_date)
            if match:
                month = int(match.group(1))
                day = int(match.group(2))
                current_year = datetime.now().year

                # 生成多种格式
                formats.extend([
                    f"{current_year}/{month}/{day}",      # 2025/7/24
                    f"{current_year}-{month}-{day}",      # 2025-7-24
                    f"{month}/{day}",                     # 7/24
                    f"{month:02d}/{day:02d}",            # 07/24
                    f"{current_year}/{month:02d}/{day:02d}",  # 2025/07/24
                ])

        # 如果是斜杠格式，也生成中文格式
        elif '/' in target_date:
            parts = target_date.split('/')
            if len(parts) >= 2:
                try:
                    if len(parts) == 3:  # 2025/7/24
                        month = int(parts[1])
                        day = int(parts[2])
                    else:  # 7/24
                        month = int(parts[0])
                        day = int(parts[1])

                    formats.append(f"{month}月{day}日")
                except ValueError:
                    pass

        return formats

    def _is_date_match(self, cell_value: str, target_formats: List[str]) -> bool:
        """
        检查单元格值是否与任何目标格式匹配
        """
        cell_value = cell_value.strip()

        for target_format in target_formats:
            # 精确匹配
            if cell_value == target_format:
                return True

            # 包含匹配
            if target_format in cell_value or cell_value in target_format:
                return True

        return False

    def _smart_find_date_row(self, sheet_id: str, target_date: str, start_row: int) -> Optional[int]:
        """
        智能查找日期行 - 基于日期的年月日数值进行匹配
        例如：目标是'7月24日'，能找到表格中的'2025/7/24'
        """
        try:
            import re
            from datetime import datetime

            print(f"🧠 智能查找模式：分析目标日期 '{target_date}'")

            # 从目标日期中提取月和日
            target_month = None
            target_day = None

            if '月' in target_date and '日' in target_date:
                match = re.search(r'(\d+)月(\d+)日', target_date)
                if match:
                    target_month = int(match.group(1))
                    target_day = int(match.group(2))

            if target_month is None or target_day is None:
                print(f"❌ 无法从 '{target_date}' 中提取月日信息")
                return None

            print(f"🎯 提取到目标：{target_month}月{target_day}日")

            # 搜索表格中的所有日期
            max_search_rows = 200
            search_range = f"A{start_row}:A{start_row + max_search_rows}"
            column_data = self.dingtalk_sheet.get_cell_range(sheet_id, search_range)

            if not column_data:
                return None

            for i, row in enumerate(column_data):
                if row and len(row) > 0:
                    cell_value = str(row[0]).strip()
                    if cell_value:
                        # 尝试从各种格式中提取月日
                        cell_month, cell_day = self._extract_month_day(cell_value)

                        if cell_month == target_month and cell_day == target_day:
                            found_row = start_row + i
                            print(f"✅ 智能匹配成功！'{cell_value}' 对应 {target_month}月{target_day}日，在第 {found_row} 行")
                            return found_row

            print(f"❌ 智能查找失败：未找到 {target_month}月{target_day}日 对应的行")
            return None

        except Exception as e:
            print(f"❌ 智能查找异常: {str(e)}")
            return None

    def _extract_month_day(self, date_str: str) -> tuple:
        """
        从各种日期格式中提取月和日
        支持：2025/7/24, 2025-7-24, 7月24日, 7/24 等
        """
        import re

        try:
            # 格式1：2025/7/24 或 7/24
            if '/' in date_str:
                parts = date_str.split('/')
                if len(parts) >= 2:
                    if len(parts) == 3:  # 2025/7/24
                        return int(parts[1]), int(parts[2])
                    else:  # 7/24
                        return int(parts[0]), int(parts[1])

            # 格式2：2025-7-24 或 7-24
            elif '-' in date_str:
                parts = date_str.split('-')
                if len(parts) >= 2:
                    if len(parts) == 3:  # 2025-7-24
                        return int(parts[1]), int(parts[2])
                    else:  # 7-24
                        return int(parts[0]), int(parts[1])

            # 格式3：7月24日
            elif '月' in date_str and '日' in date_str:
                match = re.search(r'(\d+)月(\d+)日', date_str)
                if match:
                    return int(match.group(1)), int(match.group(2))

        except (ValueError, IndexError):
            pass

        return None, None

    def _create_missing_date_row(self, sheet_id: str, target_date: str, start_row: int, existing_rows: int) -> Optional[int]:
        """创建缺失的日期行"""
        try:
            print(f"🔧 尝试创建缺失的日期行: {target_date}")

            # 计算应该插入的位置（在现有日期行的末尾）
            insert_row = start_row + existing_rows

            # 写入日期
            date_range = f"A{insert_row}:A{insert_row}"
            success = self.dingtalk_sheet.write_cell_range(sheet_id, date_range, [[target_date]])

            if success:
                print(f"✅ 成功创建日期行 '{target_date}' 在第 {insert_row} 行")
                return insert_row
            else:
                print(f"❌ 创建日期行失败")
                return None

        except Exception as e:
            print(f"❌ 创建日期行异常: {str(e)}")
            return None

    def _create_monthly_table(self, sheet_id: str, table_title: str, year: str, month_cn: str) -> Optional[int]:
        """创建月度汇总表"""
        try:
            print(f"🔧 开始创建月度汇总表: {table_title}")

            # 1. 找到一个空白区域来创建表格（从A列的空白行开始）
            range_to_scan = "A1:A200"
            column_data = self.dingtalk_sheet.get_cell_range(sheet_id, range_to_scan)

            start_row = 1
            if column_data:
                # 找到最后一个有数据的行
                for i, row in enumerate(column_data):
                    if row and str(row[0]).strip():
                        start_row = i + 3  # 留两行空白

            print(f"📍 将在第 {start_row} 行开始创建汇总表")

            # 2. 创建表头
            headers = [
                "日期", "当前日期总记录数", "平台代发订单-未改单数",
                "平台代发订单-未改单原始单号", "断货阶段未备注",
                "改动成本错误或未改单数", "改动成本错误或未改单实际总数",
                "报价严重超时", "报价超时"
            ]

            # 3. 写入标题行并合并居中 - 使用_merge_cells方法确保格式正确
            title_range = f"A{start_row}:{chr(ord('A') + len(headers) - 1)}{start_row}"  # 合并整行

            # 使用_merge_cells方法同时处理合并、写入值和格式设置
            success = self.dingtalk_sheet._merge_cells(sheet_id, title_range, table_title)
            if not success:
                print("❌ 写入和格式化标题失败")
                return None

            print(f"✅ 标题行已合并居中: {title_range} = '{table_title}'")

            # 4. 写入表头
            header_row = start_row + 1
            header_range = f"A{header_row}:{chr(ord('A') + len(headers) - 1)}{header_row}"
            success = self.dingtalk_sheet.write_cell_range(sheet_id, header_range, [headers])
            if not success:
                print("❌ 写入表头失败")
                return None

            # 5. 创建日期行（1-31日）
            month_num = int(month_cn.replace('月', ''))
            import calendar
            days_in_month = calendar.monthrange(int(year), month_num)[1]

            for day in range(1, days_in_month + 1):
                day_row = header_row + day
                date_str = f"{month_num}月{day}日"
                date_range = f"A{day_row}:A{day_row}"
                success = self.dingtalk_sheet.write_cell_range(sheet_id, date_range, [[date_str]])
                if not success:
                    print(f"⚠️ 写入日期 {date_str} 失败")

            print(f"✅ 成功创建月度汇总表，标题行在第 {start_row} 行")
            return start_row

        except Exception as e:
            print(f"❌ 创建月度汇总表失败: {str(e)}")
            return None

    def write_monthly_summary(self, data: Dict, target_date: str = None) -> bool:
        """写入月度汇总数据"""
        try:
            print("📝 准备写入月度汇总报告...")

            # 1. 获取指定日期或当前月份信息
            year, month_cn, current_date_cn = self.get_current_month_info(target_date)

            # 2. 查找结果工作表
            target_sheet_id = self.actual_sheet_id
            print(f"✅ 找到结果工作表: {self.result_sheet_name} (ID: {target_sheet_id})")

            # 3. 查找或创建月度汇总表
            table_title = f"{year}年{month_cn.zfill(2)}数据汇总"
            header_row_num = self._find_monthly_table(target_sheet_id, table_title)

            if header_row_num is None:
                print(f"⚠️ 未找到月度汇总表: {table_title}")
                print("🔧 开始创建月度汇总表...")
                title_row_num = self._create_monthly_table(target_sheet_id, table_title, year, month_cn)

                if title_row_num is None:
                    print(f"❌ 创建月度汇总表失败")
                    return False

                # 创建成功后，表头在标题行的下一行
                header_row_num = title_row_num + 1
                print(f"✅ 月度汇总表创建成功，表头在第 {header_row_num} 行")

            # 4. 查找当天日期对应的行号
            current_date_str = current_date_cn
            target_row_num = self._find_date_row(target_sheet_id, current_date_str, header_row_num)

            if target_row_num is None:
                print(f"❌ 未找到日期 '{current_date_str}' 对应的行")
                print(f"🔍 尝试智能查找表格中的日期格式...")

                # 尝试智能查找现有的日期格式
                target_row_num = self._smart_find_date_row(target_sheet_id, current_date_str, header_row_num)

                if target_row_num is None:
                    print(f"❌ 智能查找也失败，无法找到对应的日期行")
                    return False

            # 5. 准备写入数据
            headers = [
                "日期", "当前日期总记录数", "平台代发订单-未改单数",
                "平台代发订单-未改单原始单号", "断货阶段未备注",
                "改动成本错误或未改单数", "改动成本错误或未改单实际总数",
                "报价严重超时", "报价超时"
            ]

            # 构建数据行
            row_data_values = []
            row_data_values.append(str(data.get('total_records', '暂无数据')))
            row_data_values.append(str(data.get('empty_purchase_records', '暂无数据')))
            original_numbers = data.get('empty_purchase_original_numbers', [])
            # 处理原始单号，确保长数字以文本格式写入，避免科学计数法
            if original_numbers:
                # 将所有单号连接，确保长数字保持文本格式
                formatted_numbers = []
                for num in original_numbers:
                    num_str = str(num).strip()
                    if num_str:
                        # 移除可能已存在的单引号前缀，然后重新处理
                        if num_str.startswith("'"):
                            num_str = num_str[1:]

                        # 对于长数字（超过10位），在前面添加制表符强制文本格式
                        if len(num_str) > 10 and num_str.isdigit():
                            # 使用制表符前缀，这在Excel和钉钉表格中都能强制文本格式
                            formatted_numbers.append(f"\t{num_str}")
                        else:
                            formatted_numbers.append(num_str)
                row_data_values.append("\n".join(formatted_numbers))
            else:
                row_data_values.append("无")
            row_data_values.append(str(data.get('negative_stock_empty_remark_records', 0)))
            row_data_values.append(str(data.get('cost_error_count_records', 0)))
            row_data_values.append(str(data.get('cost_error_total_records', 0)))
            row_data_values.append(str(data.get('quote_serious_timeout_records', 0)))
            row_data_values.append(str(data.get('quote_timeout_records', 0)))

            target_range = f"B{target_row_num}:{chr(ord('A') + len(headers) - 1)}{target_row_num}"

            print(f"✍️ 正在写入数据到第 {target_row_num} 行 (范围: {target_range})...")
            print(f"🔍 调试信息:")
            print(f"  URL: https://api.dingtalk.com/v1.0/doc/workbooks/{target_sheet_id}/sheets/{target_sheet_id}/ranges/{target_range}?operatorId=SslfRpH3glxew7XC8gCkdgiEiE")
            print(f"  数据: {[row_data_values]}")

            # 6. 写入数据
            success = self.dingtalk_sheet.write_cell_range(target_sheet_id, target_range, [row_data_values])

            if success:
                print(f"✅ 成功写入数据到工作表 {target_sheet_id}!{target_range}")
                print(f"✅ 成功写入数据到 {target_range}")

                # 7. 设置包含长数字的单元格为文本格式，防止科学计数法显示
                # 原始单号在第4列（D列），对应索引3
                if original_numbers and len(original_numbers) > 0:
                    # 检查是否有长数字需要设置文本格式
                    has_long_numbers = any(len(str(num).strip()) > 10 and str(num).strip().isdigit() for num in original_numbers)
                    if has_long_numbers:
                        # 设置D列单元格为文本格式
                        original_number_cell = f"D{target_row_num}"
                        format_success = self.dingtalk_sheet.set_cell_format(target_sheet_id, original_number_cell, "@")
                        if format_success:
                            print(f"✅ 成功设置单元格 {original_number_cell} 为文本格式，防止长数字显示为科学计数法")
                        else:
                            print(f"⚠️ 设置单元格 {original_number_cell} 文本格式失败，但数据已写入")

                # 8. 设置标题行格式（合并居中）
                # 注意：经过测试，钉钉开放平台API不支持合并单元格操作
                # 数据写入功能正常，这是最重要的功能
                print("ℹ️ 钉钉开放平台API不支持合并单元格操作，标题行保持默认格式")

                return True
            else:
                print(f"❌ 写入数据失败")
                return False

        except Exception as e:
            print(f"❌ 写入月度汇总失败: {str(e)}")
            return False




class ExcelDataReader:
    """Excel数据读取器 - 负责从Excel文件读取所有数据"""
    
    def __init__(self, config_path: str = "config.json"):
        """初始化Excel数据读取器"""
        self.config_path = config_path
        self.config = self._load_config()
        
        # 从配置文件获取Excel相关配置
        excel_config = self.config.get('excel_checker', {})
        self.file_path = excel_config.get('file_path')
        self.sheet_name = excel_config.get('sheet_name', '采购部')
        self.column_keywords = excel_config.get('column_keywords', {})
        self.empty_values = excel_config.get('empty_values', ['', 'none', 'null', 'nan'])
        
        print(f"📁 Excel文件路径: {self.file_path}")
        print(f"📄 目标工作表: {self.sheet_name}")
        print("✅ Excel数据读取器初始化完成")

    def _load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 加载配置文件失败: {str(e)}")
            return {}

    def get_current_date_info(self, target_date: str = None) -> Tuple[str, str]:
        """获取指定日期或前一天的日期信息"""
        if target_date:
            # 使用指定的日期
            try:
                target_dt = datetime.strptime(target_date, '%Y-%m-%d')
                standard_date = target_dt.strftime('%Y-%m-%d')
                print(f"📅 使用指定日期: {standard_date}")
            except ValueError:
                print(f"❌ 日期格式错误: {target_date}，使用前一天日期")
                target_dt = datetime.now() - timedelta(days=1)
                standard_date = target_dt.strftime('%Y-%m-%d')
        else:
            # 默认使用前一天的日期
            target_dt = datetime.now() - timedelta(days=1)
            standard_date = target_dt.strftime('%Y-%m-%d')

        month_num = int(target_dt.month)
        current_date_cn = f"{month_num}月{target_dt.day}日"

        print(f"📅 处理日期: {standard_date} ({current_date_cn})")
        return standard_date, current_date_cn

    def read_excel_file(self) -> Optional[pd.DataFrame]:
        """读取Excel文件"""
        try:
            if not os.path.exists(self.file_path):
                print(f"❌ Excel文件不存在: {self.file_path}")
                return None

            print(f"📖 正在读取Excel文件: {self.file_path}")
            print(f"📄 目标工作表: {self.sheet_name}")

            # 读取Excel文件，强制将可能包含长数字的列读取为字符串格式
            # 这样可以防止pandas将长数字自动转换为科学计数法
            dtype_dict = {}

            # 对于可能包含长数字的列，强制设置为字符串类型
            potential_long_number_keywords = ['原始单号', '单号', '订单号', 'ID', 'id']

            # 先读取列名
            temp_df = pd.read_excel(self.file_path, sheet_name=self.sheet_name, nrows=0)
            for col in temp_df.columns:
                col_str = str(col)
                # 如果列名包含可能的长数字关键词，设置为字符串类型
                if any(keyword in col_str for keyword in potential_long_number_keywords):
                    dtype_dict[col] = str
                    print(f"🔧 设置列 '{col}' 为字符串类型，防止长数字转换为科学计数法")

            # 使用指定的数据类型读取Excel文件
            df = pd.read_excel(self.file_path, sheet_name=self.sheet_name, dtype=dtype_dict)

            if df.empty:
                print("⚠️ Excel工作表为空")
                return None

            print(f"✅ 成功读取Excel文件，共{len(df)}行数据")
            print(f"📋 列名: {list(df.columns)}")

            return df

        except Exception as e:
            print(f"❌ 读取Excel文件失败: {str(e)}")
            return None

    def find_column_indexes(self, df: pd.DataFrame) -> Dict[str, Optional[str]]:
        """查找目标列名"""
        columns = {}
        
        # 获取所有需要的列关键词
        for key, keyword in self.column_keywords.items():
            columns[key] = None
            for col in df.columns:
                col_str = str(col).strip()
                if keyword in col_str:
                    columns[key] = col
                    print(f"  ✅ 找到{keyword}列: {col}")
                    break
            
            if columns[key] is None:
                print(f"  ⚠️ 未找到'{keyword}'列")
        
        return columns

    def analyze_data(self, df: pd.DataFrame, target_date_cn: str, columns: Dict[str, Optional[str]]) -> Dict:
        """分析Excel数据，提取所有需要的指标"""
        print(f"🔍 开始分析Excel数据，查找日期为'{target_date_cn}'的记录...")
        
        # 初始化结果
        result = {
            'current_date': target_date_cn,
            'total_records': 0,
            'empty_purchase_records': 0,
            'empty_purchase_original_numbers': [],
            'negative_stock_empty_remark_records': 0,
            'cost_error_count_records': 0,
            'cost_error_total_records': 0,
            'quote_serious_timeout_records': 0,
            'quote_timeout_records': 0,
        }
        
        all_dates_found = set()
        
        # 遍历数据行
        for index, row in df.iterrows():
            try:
                # 获取日期值
                date_col = columns.get('date_column')
                if not date_col:
                    continue
                    
                date_value = str(row[date_col]).strip() if pd.notna(row[date_col]) else ""
                
                # 收集所有找到的日期
                if date_value:
                    all_dates_found.add(date_value)

                # 检查日期是否匹配（支持多种日期格式）
                date_matches = self._check_date_match(date_value, target_date_cn)
                
                if date_matches:
                    print(f"  📊 找到匹配日期的数据行: {index+1}")
                    
                    # 提取各个字段的值
                    self._extract_field_values(row, columns, result)
                    
            except Exception as e:
                print(f"⚠️ 处理第{index+2}行数据时出错: {str(e)}")
                continue

        # 显示Excel中所有找到的日期
        if all_dates_found:
            print(f"📅 Excel中找到的所有日期: {sorted(list(all_dates_found))}")
        
        print(f"\n📊 Excel分析结果:")
        for key, value in result.items():
            if key != 'current_date':
                print(f"  - {key}: {value}")

        return result

    def _check_date_match(self, date_value: str, target_date_cn: str) -> bool:
        """检查日期是否匹配 - 支持多种日期格式的智能匹配"""
        import re
        from datetime import datetime

        # 精确匹配
        if date_value == target_date_cn:
            return True
        elif target_date_cn in date_value:
            return True

        # 从中文日期格式中提取月日信息
        if '月' in target_date_cn and '日' in target_date_cn:
            match = re.search(r'(\d+)月(\d+)日', target_date_cn)
            if match:
                target_month = int(match.group(1))
                target_day = int(match.group(2))
                current_year = datetime.now().year

                # 生成多种可能的日期格式进行匹配
                possible_formats = [
                    f"{current_year}-{target_month:02d}-{target_day:02d}",  # 2025-07-31
                    f"{current_year}-{target_month}-{target_day}",          # 2025-7-31
                    f"{current_year}/{target_month:02d}/{target_day:02d}",  # 2025/07/31
                    f"{current_year}/{target_month}/{target_day}",          # 2025/7/31
                    f"{target_month}/{target_day}",                         # 7/31
                    f"{target_month:02d}/{target_day:02d}",                # 07/31
                ]

                # 检查是否匹配任何一种格式
                for format_str in possible_formats:
                    if (date_value == format_str or
                        date_value.startswith(format_str) or
                        format_str in date_value):
                        return True

        return False

    def _extract_field_values(self, row, columns: Dict[str, Optional[str]], result: Dict):
        """从行中提取各个字段的值"""
        # 提取总记录数
        total_col = columns.get('total_records_column')
        if total_col and pd.notna(row[total_col]):
            try:
                result['total_records'] = int(float(row[total_col]))
            except (ValueError, TypeError):
                pass
        
        # 提取未改单数
        empty_purchase_col = columns.get('empty_purchase_records_column')
        if empty_purchase_col and pd.notna(row[empty_purchase_col]):
            try:
                result['empty_purchase_records'] = int(float(row[empty_purchase_col]))
            except (ValueError, TypeError):
                pass
        
        # 提取原始单号
        original_numbers_col = columns.get('original_numbers_column')
        if original_numbers_col and pd.notna(row[original_numbers_col]):
            original_numbers = str(row[original_numbers_col]).strip()
            if original_numbers and original_numbers not in self.empty_values:
                # 如果有多个单号，用换行符分割，并确保每个单号都是字符串格式
                numbers_list = original_numbers.split('\n')
                # 处理长数字，确保作为文本格式保存，避免科学计数法
                processed_numbers = []
                for num in numbers_list:
                    num = num.strip()
                    if num:
                        # 直接保存原始字符串，在写入时再处理格式
                        processed_numbers.append(num)
                result['empty_purchase_original_numbers'] = processed_numbers
        
        # 提取断货阶段未备注
        remark_col = columns.get('remark_column')
        if remark_col and pd.notna(row[remark_col]):
            try:
                result['negative_stock_empty_remark_records'] = int(float(row[remark_col]))
            except (ValueError, TypeError):
                pass
        
        # 提取改动成本错误数量
        cost_error_count_col = columns.get('cost_error_count_column')
        if cost_error_count_col and pd.notna(row[cost_error_count_col]):
            try:
                result['cost_error_count_records'] = int(float(row[cost_error_count_col]))
            except (ValueError, TypeError):
                pass
        
        # 提取改动成本错误总数
        cost_error_total_col = columns.get('cost_error_total_column')
        if cost_error_total_col and pd.notna(row[cost_error_total_col]):
            try:
                result['cost_error_total_records'] = int(float(row[cost_error_total_col]))
            except (ValueError, TypeError):
                pass
        
        # 提取报价严重超时
        quote_serious_timeout_col = columns.get('quote_serious_timeout_column')
        if quote_serious_timeout_col and pd.notna(row[quote_serious_timeout_col]):
            try:
                result['quote_serious_timeout_records'] = int(float(row[quote_serious_timeout_col]))
            except (ValueError, TypeError):
                pass
        
        # 提取报价超时
        quote_timeout_col = columns.get('quote_timeout_column')
        if quote_timeout_col and pd.notna(row[quote_timeout_col]):
            try:
                result['quote_timeout_records'] = int(float(row[quote_timeout_col]))
            except (ValueError, TypeError):
                pass

    def run_analysis(self, target_date: str = None) -> Dict:
        """运行完整的Excel数据分析"""
        try:
            print("🚀 开始执行Excel数据分析...")

            # 1. 获取指定日期或当前日期信息
            _, current_date_cn = self.get_current_date_info(target_date)

            # 2. 读取Excel文件
            df = self.read_excel_file()
            if df is None:
                print("⚠️ Excel文件读取失败，返回默认值")
                return self._get_default_result(current_date_cn)

            # 3. 查找列索引
            columns = self.find_column_indexes(df)

            # 4. 分析数据
            result = self.analyze_data(df, current_date_cn, columns)

            print("✅ Excel数据分析完成!")
            return result

        except Exception as e:
            print(f"❌ Excel数据分析失败: {str(e)}")
            _, current_date_cn = self.get_current_date_info(target_date)
            return self._get_default_result(current_date_cn)

    def _get_default_result(self, current_date_cn: str) -> Dict:
        """获取默认结果结构"""
        return {
            'current_date': current_date_cn,
            'total_records': '暂无数据',
            'empty_purchase_records': '暂无数据',
            'empty_purchase_original_numbers': [],
            'negative_stock_empty_remark_records': 0,
            'cost_error_count_records': 0,
            'cost_error_total_records': 0,
            'quote_serious_timeout_records': 0,
            'quote_timeout_records': 0,
        }


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='采购部绩效数据拉取与汇总系统')
    parser.add_argument('-d', '--date', type=str, help='指定处理日期 (格式: YYYY-MM-DD，如: 2025-07-20)')
    args = parser.parse_args()

    target_date = args.date
    if target_date:
        print(f"🎯 指定处理日期: {target_date}")
    else:
        print("🎯 使用默认模式: 处理前一天的数据")

    print("=" * 60)
    print("🚀 采购部绩效数据拉取与汇总系统启动")
    print("=" * 60)

    try:
        # 1. 初始化Excel数据读取器
        print("\n" + "=" * 60)
        print("📦 第一步：初始化Excel数据读取器")
        print("=" * 60)
        excel_reader = ExcelDataReader()

        # 2. 读取和分析Excel数据
        print("\n" + "=" * 60)
        print("📊 第二步：读取和分析Excel数据")
        print("=" * 60)
        excel_result = excel_reader.run_analysis(target_date)

        # 3. 初始化结果写入器
        print("\n" + "=" * 60)
        print("✍️ 第三步：初始化结果写入器")
        print("=" * 60)
        result_writer = ResultWriter()

        # 4. 写入月度汇总
        print("\n" + "=" * 60)
        print("📝 第四步：写入月度汇总")
        print("=" * 60)
        write_success = result_writer.write_monthly_summary(excel_result, target_date)

        if write_success:
            print("\n✅ 数据已成功写入/更新到月度汇总")
        else:
            print("\n❌ 数据写入失败")

        print("\n" + "=" * 80)
        print("🎯 请前往钉钉表格查看'采购部绩效明细'工作表获取最新汇总。")

    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        # 防止exe运行完立即关闭窗口
        print("\n" + "=" * 60)
        print("程序执行完成！")
        input("按回车键退出程序...")


if __name__ == "__main__":
    main()
