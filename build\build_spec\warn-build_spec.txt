
This file lists modules Py<PERSON>nst<PERSON><PERSON> was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pyimod02_importers - imported by C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level), numpy.lib._npyio_impl (top-level), http.client (top-level), numpy.lib._function_base_impl (top-level), numpy._typing._nested_sequence (conditional), numpy._typing._shape (top-level), numpy._typing._dtype_like (top-level), numpy._typing._array_like (top-level), asyncio.base_events (top-level), asyncio.coroutines (top-level), pyreadline3.py3k_compat (top-level), yaml.constructor (top-level), typing_extensions (top-level), pandas.util._exceptions (conditional), pandas._typing (top-level), pytz.lazy (optional), pkg_resources (top-level), setuptools (top-level), jaraco.functools (top-level), more_itertools.more (top-level), more_itertools.recipes (top-level), setuptools._distutils.spawn (top-level), setuptools._reqs (top-level), setuptools.discovery (top-level), setuptools.dist (top-level), setuptools._distutils.core (top-level), setuptools._distutils.cmd (top-level), setuptools._distutils.dist (top-level), setuptools._distutils.fancy_getopt (top-level), configparser (top-level), setuptools.config.setupcfg (top-level), setuptools.config.expand (top-level), setuptools.config.pyprojecttoml (top-level), setuptools.config._apply_pyprojecttoml (top-level), tomllib._parser (top-level), setuptools._vendor.tomli._parser (top-level), setuptools.command.egg_info (top-level), setuptools.glob (top-level), setuptools.command._requirestxt (top-level), setuptools.command.bdist_wheel (top-level), setuptools._vendor.wheel.cli.tags (top-level), setuptools._vendor.platformdirs.windows (conditional), pandas.core.arrays.arrow.accessors (conditional), pandas.util._decorators (conditional), pandas.util._validators (top-level), pandas.util.version (top-level), pandas.core.dtypes.inference (conditional), pandas.core.construction (top-level), pandas.core.common (top-level), pandas.core.frame (top-level), pandas.core.dtypes.concat (conditional), pandas.core.sorting (conditional), pandas.core.indexes.category (conditional), pandas.core.arrays.masked (conditional), pandas.core.apply (conditional), pandas.core.base (conditional), pandas.core.indexing (conditional), pandas.core.internals.blocks (conditional), pandas.core.strings.base (conditional), pandas.core.strings.object_array (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.arrays.timedeltas (conditional), pandas.io.formats.format (top-level), pandas.core.indexes.range (top-level), pandas.core.tools.timedeltas (conditional), pandas.core.indexes.datetimelike (conditional), pandas.core.reshape.concat (conditional), pandas.io.common (top-level), pandas.io.formats.printing (top-level), pandas.core.indexes.multi (top-level), pandas.io.formats.html (conditional), pandas.io.formats.string (conditional), pandas.io.formats.csvs (top-level), pandas.io.formats.style_render (top-level), pandas.core.interchange.dataframe_protocol (conditional), pandas.core.window.rolling (conditional), pandas.core.series (top-level), pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (conditional), pandas.core.methods.selectn (top-level), pandas.core.strings.accessor (conditional), pandas.core.tools.datetimes (conditional), pandas.io.formats.info (conditional), pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas.core.groupby.grouper (conditional), pandas.core.groupby.ops (conditional), pandas.io.json._normalize (conditional), pandas.io.parsers.base_parser (conditional), pandas.io.parsers.c_parser_wrapper (conditional), pandas.io.parsers.python_parser (top-level), pandas.io.parsers.readers (conditional), pandas.io.json._json (conditional), pandas.io.stata (conditional), markupsafe (top-level), pandas.io.formats.style (conditional), pandas.io.formats.excel (top-level), pandas.io.formats.css (conditional), pandas.io.excel._base (top-level), pandas.io.excel._util (top-level), xml.etree.ElementTree (top-level), PIL.Image (top-level), PIL._typing (top-level), PIL.ImageFilter (top-level), PIL.ImagePalette (top-level), PIL.TiffImagePlugin (top-level), PIL.ImageOps (top-level), PIL.PngImagePlugin (top-level), pandas.core.arrays.interval (conditional), pandas.core.indexes.interval (conditional), pandas.core.arrays.period (conditional), pandas.core.indexes.period (conditional), pandas.core.internals.managers (top-level), pandas.core.internals.ops (conditional), pandas.core.internals.array_manager (conditional), pandas.core.internals.concat (conditional), pandas.core.internals.construction (conditional), pandas.core.methods.describe (conditional), pandas.core.generic (conditional), pandas.core.computation.parsing (conditional), pandas.compat.pickle_compat (conditional), pandas.core.computation.ops (conditional), pandas.core.computation.align (conditional), pandas.io.pytables (conditional), pandas.io.sql (conditional), sqlalchemy.util.compat (conditional), sqlalchemy.util.typing (top-level), sqlalchemy.sql.coercions (top-level), sqlalchemy.sql.traversals (top-level), sqlalchemy.sql.compiler (top-level), sqlalchemy.sql.dml (top-level), sqlalchemy.sql.sqltypes (top-level), sqlalchemy.engine.row (top-level), sqlalchemy.sql.lambdas (top-level), sqlalchemy.engine.url (top-level), sqlalchemy.orm.query (top-level), sqlite3.dbapi2 (top-level), pandas.core.groupby.groupby (top-level), pandas.core.groupby.base (conditional), pandas.core.groupby.indexing (top-level), pandas.core.resample (conditional), pandas.core.groupby.generic (conditional), pandas.core.reshape.merge (top-level), pandas.core.arrays.numeric (conditional), pandas.core.arrays.datetimelike (conditional), pandas.core.arrays.datetimes (conditional), pandas.core.indexes.datetimes (conditional), pandas.core.arrays._mixins (conditional), pandas.core.arrays.categorical (conditional), pandas.core.reshape.melt (conditional), pandas.core.interchange.dataframe (conditional), pandas.io.feather_format (conditional), fsspec.utils (top-level), fsspec.dircache (top-level), fsspec.asyn (top-level), fsspec.implementations.cache_metadata (conditional), fsspec.json (top-level), fsspec.mapping (top-level), pandas.io.xml (conditional), pandas.core.reshape.pivot (top-level), pandas.core.arrays.base (conditional), pandas.core.indexes.base (conditional), pandas.core.dtypes.cast (conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.dtypes.dtypes (conditional), pandas._config.config (conditional), pandas.core.util.hashing (conditional), pandas.core.reshape.encoding (top-level), pandas._config.localization (conditional), pandas._testing.contexts (conditional), pandas._testing._warnings (conditional), pandas.io.html (conditional), html5lib._utils (optional), html5lib._trie._base (optional), html5lib.treebuilders.dom (optional), html5lib.treebuilders.etree_lxml (optional), lxml.html (top-level), lxml.html._setmixin (optional), pandas.io.sas.sasreader (conditional), pandas.io.spss (conditional), requests.compat (top-level), socks (optional), jaraco.classes.ancestry (conditional), jaraco.classes.properties (conditional), jaraco.collections (top-level), setuptools._vendor.typeguard._checkers (top-level), setuptools._vendor.typeguard._config (top-level), setuptools._vendor.typeguard._suppression (top-level), setuptools._vendor.typeguard._decorators (top-level), setuptools._vendor.typeguard._transformer (top-level), setuptools._vendor.typeguard._importhook (top-level), fsspec.gui (top-level), multidict._abc (top-level), attr._compat (top-level), yarl._query (top-level), yarl._url (top-level), yarl._path (top-level), aiohttp.web (top-level), aiohttp.abc (top-level), frozenlist (top-level), aiohttp.client_reqrep (top-level), aiohttp.multipart (top-level), aiohttp.compression_utils (conditional), aiohttp.payload (top-level), aiohappyeyeballs.types (top-level), aiohttp.connector (conditional), aiohttp.web_response (top-level), aiohttp.client_middlewares (top-level), aiohttp.cookiejar (top-level), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.groupby (conditional), pandas.plotting._matplotlib.boxplot (conditional), PIL.Jpeg2KImagePlugin (top-level), PIL.IptcImagePlugin (top-level), _pyrepl.types (top-level), _pyrepl.readline (top-level), sqlalchemy.ext.baked (top-level), trio._core._entry_queue (top-level), trio._util (top-level), exceptiongroup._catch (top-level), exceptiongroup._exceptions (top-level), exceptiongroup._formatting (top-level), sortedcontainers.sortedlist (optional), sortedcontainers.sortedset (optional), sortedcontainers.sorteddict (optional), trio._socket (conditional), trio._core._parking_lot (conditional), trio._threads (conditional), trio._core._traps (conditional), trio._subprocess (conditional), trio._deprecate (conditional), trio._core._asyncgens (conditional), trio._core._instrumentation (conditional), trio._core._thread_cache (conditional), trio._core._run (conditional), trio.testing._check_streams (top-level), trio.testing._checkpoints (conditional), trio.testing._memory_streams (top-level), trio._highlevel_socket (conditional), trio.testing._raises_group (conditional), trio.testing._sequencer (conditional), trio.testing._trio_test (conditional), trio._file_io (top-level), trio._core._io_windows (conditional), trio._core._generated_io_kqueue (conditional), trio._core._io_kqueue (conditional), trio._core._generated_run (conditional), trio._core._ki (conditional), trio._channel (top-level), trio._dtls (conditional), trio._highlevel_open_tcp_listeners (conditional), trio._highlevel_open_tcp_stream (conditional), trio._highlevel_open_unix_stream (conditional), trio._highlevel_serve_listeners (top-level), trio._highlevel_ssl_helpers (conditional), trio._path (conditional), trio._signals (conditional), trio._ssl (conditional), trio._timeouts (conditional), anyio._core._eventloop (top-level), anyio.abc._eventloop (top-level), anyio._core._exceptions (top-level), anyio._core._tasks (top-level), anyio.abc._tasks (top-level), anyio._core._testing (top-level), anyio.from_thread (top-level), anyio.abc._sockets (top-level), anyio._core._typedattr (top-level), anyio.abc._streams (top-level), anyio._core._sockets (top-level), anyio.to_thread (top-level), anyio.streams.stapled (top-level), anyio.streams.tls (top-level), anyio.abc._testing (top-level), anyio._core._fileio (top-level), anyio._core._signals (top-level), anyio._core._subprocesses (top-level), httpx._models (top-level), markdown_it.main (top-level), markdown_it._compat (top-level), markdown_it.ruler (top-level), markdown_it.utils (top-level), markdown_it.token (top-level), markdown_it.common.normalize_url (top-level), mdurl._decode (top-level), mdurl._encode (top-level), markdown_it.renderer (top-level), rich.scope (top-level), dns.immutable (top-level), sqlalchemy.dialects.postgresql.psycopg2 (top-level), anyio._backends._asyncio (top-level), anyio._core._asyncio_selector_thread (top-level), anyio._backends._trio (top-level), PIL.ImageDraw (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named posix - imported by os (conditional, optional), posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), _pyrepl.unix_console (delayed, optional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional), multiprocessing.util (delayed)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named _scproxy - imported by urllib.request (conditional)
missing module named termios - imported by tty (top-level), _pyrepl.pager (delayed, optional), getpass (optional), tqdm.utils (delayed, optional), _pyrepl.unix_console (top-level), _pyrepl.fancy_termios (top-level), _pyrepl.unix_eventqueue (top-level), click._termui_impl (conditional)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), http.server (delayed, optional), netrc (delayed, conditional), getpass (delayed, optional), psutil (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional)
missing module named urllib.urlopen - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named urllib.urlencode - imported by urllib (delayed, optional), lxml.html (delayed, optional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named annotationlib - imported by typing_extensions (conditional), attr._compat (conditional)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional)
missing module named java - imported by platform (delayed)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), setuptools._distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named _curses - imported by curses (top-level), curses.has_key (top-level), _pyrepl.curses (optional)
missing module named fcntl - imported by subprocess (optional), tqdm.utils (delayed, optional), _pyrepl.unix_console (top-level), pty (delayed, optional)
missing module named startup - imported by pyreadline3.keysyms.common (conditional), pyreadline3.keysyms.keysyms (conditional)
missing module named sets - imported by pyreadline3.keysyms.common (optional), pytz.tzinfo (optional)
missing module named System - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.keysyms.ironpython_keysyms (top-level), pyreadline3.console.ironpython_console (top-level), pyreadline3.rlmain (conditional)
missing module named console - imported by pyreadline3.console.ansi (conditional)
missing module named clr - imported by pyreadline3.clipboard.ironpython_clipboard (top-level), pyreadline3.console.ironpython_console (top-level)
missing module named IronPythonConsole - imported by pyreadline3.console.ironpython_console (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), setuptools._vendor.wheel.vendored.packaging._manylinux (delayed, optional)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named _typeshed - imported by setuptools.glob (conditional), setuptools.compat.py311 (conditional), pkg_resources (conditional), jaraco.collections (conditional), trio._file_io (conditional), trio._path (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), httpx._transports.wsgi (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named defusedxml - imported by openpyxl.xml (delayed, optional), PIL.Image (optional), E:\RPA项目\采购部绩效数据拉取\purchase_checker_simplified.py (top-level)
missing module named zstandard - imported by fsspec.compression (optional), urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named compression - imported by fsspec.compression (optional), urllib3.util.request (optional), urllib3.response (optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional), httpx._decoders (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), aiohttp.compression_utils (optional), httpx._decoders (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level), httpx._client (delayed, conditional, optional)
missing module named six.moves.range - imported by six.moves (top-level), dateutil.rrule (top-level)
runtime module named six.moves - imported by dateutil.tz.tz (top-level), dateutil.tz._factories (top-level), dateutil.tz.win (top-level), dateutil.rrule (top-level), html5lib._inputstream (top-level), six.moves.urllib (top-level), html5lib.filters.sanitizer (top-level)
missing module named StringIO - imported by six (conditional)
missing module named dateutil.tz.tzfile - imported by dateutil.tz (top-level), dateutil.zoneinfo (top-level)
missing module named collections.Mapping - imported by collections (optional), pytz.lazy (optional), html5lib._utils (optional), html5lib._trie._base (optional), sortedcontainers.sorteddict (optional)
missing module named UserDict - imported by pytz.lazy (optional)
missing module named _dummy_thread - imported by numpy._core.arrayprint (optional), sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional)
missing module named 'numpy_distutils.cpuinfo' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.fcompiler' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named 'numpy_distutils.command' - imported by numpy.f2py.diagnose (delayed, conditional, optional)
missing module named numpy_distutils - imported by numpy.f2py.diagnose (delayed, optional)
missing module named threadpoolctl - imported by numpy.lib._utils_impl (delayed, optional)
missing module named numpy._core.zeros - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.vstack - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.void - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecmat - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.vecdot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ushort - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.unsignedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulonglong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ulong - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uintc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.uint64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.uint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ubyte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.trunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.true_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.transpose - imported by numpy._core (top-level), numpy.lib._function_base_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.trace - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.timedelta64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tensordot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.tanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.tan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.swapaxes - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sum - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.subtract - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.str_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.square - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sqrt - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.spacing - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.sort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.sinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.single - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.signedinteger - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.signbit - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.sign - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.short - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rint - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.right_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.result_type - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.remainder - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.reciprocal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.radians - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.rad2deg - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.prod - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.positive - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.pi - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.outer - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.ones - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.object_ - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.number - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.not_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.newaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.negative - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ndarray - imported by numpy._core (top-level), numpy.lib._utils_impl (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.multiply - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.moveaxis - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.modf - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.mod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.minimum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.maximum - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.max - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.matrix_transpose - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.matvec - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.matmul - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.longdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.long - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_not - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logical_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.logaddexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log1p - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.log - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.linspace - imported by numpy._core (top-level), numpy.lib._index_tricks_impl (top-level), numpy (conditional)
missing module named numpy._core.less_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.less - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.left_shift - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ldexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.lcm - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.isscalar - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.isnat - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.isnan - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.isfinite - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.intp - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.integer - imported by numpy._core (conditional), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.intc - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.int8 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int32 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.int16 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.inf - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.inexact - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.iinfo - imported by numpy._core (top-level), numpy.lib._twodim_base_impl (top-level), numpy (conditional)
missing module named numpy._core.hypot - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.hstack - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.heaviside - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.half - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater_equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.greater - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.gcd - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frompyfunc - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.frexp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.fmax - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor_divide - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.floating - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float_power - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.float32 - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.float16 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.finfo - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.fabs - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.expm1 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.exp - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.euler_gamma - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.errstate - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.equal - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.empty_like - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.empty - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.e - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.double - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.dot - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.divmod - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.divide - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.diagonal - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.degrees - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.deg2rad - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.datetime64 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.csingle - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cross - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.count_nonzero - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.copysign - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.conjugate - imported by numpy._core (conditional), numpy (conditional), numpy.fft._pocketfft (top-level)
missing module named numpy._core.conj - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.complexfloating - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.complex64 - imported by numpy._core (conditional), numpy (conditional), numpy._array_api_info (top-level)
missing module named numpy._core.clongdouble - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.character - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.ceil - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.cdouble - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.cbrt - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bytes_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.byte - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bool_ - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_xor - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_or - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_count - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.bitwise_and - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.atleast_3d - imported by numpy._core (top-level), numpy.lib._shape_base_impl (top-level), numpy (conditional)
missing module named numpy._core.atleast_2d - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.atleast_1d - imported by numpy._core (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.asarray - imported by numpy._core (top-level), numpy.lib._array_utils_impl (top-level), numpy.linalg._linalg (top-level), numpy (conditional), numpy.fft._pocketfft (top-level), numpy.fft._helper (top-level)
missing module named numpy._core.asanyarray - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.array_repr - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional)
missing module named numpy._core.array2string - imported by numpy._core (delayed), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.array - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib._polynomial_impl (top-level), numpy (conditional)
missing module named numpy._core.argsort - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.arctanh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan2 - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arctan - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsinh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arcsin - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccosh - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arccos - imported by numpy._core (conditional), numpy (conditional)
missing module named numpy._core.arange - imported by numpy._core (top-level), numpy.testing._private.utils (top-level), numpy (conditional), numpy.fft._helper (top-level)
missing module named numpy._core.amin - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.amax - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._core.all - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy.testing._private.utils (delayed), numpy (conditional)
missing module named numpy._core.add - imported by numpy._core (top-level), numpy.linalg._linalg (top-level), numpy (conditional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named importlib_resources - imported by setuptools._vendor.jaraco.text (optional), tqdm.cli (delayed, conditional, optional)
missing module named typeshed - imported by setuptools._vendor.typeguard._decorators (conditional)
missing module named pytest - imported by pandas._testing._io (delayed), pandas._testing (delayed), pkg_resources.tests.test_find_distributions (top-level), pkg_resources.tests.test_integration_zope_interface (top-level), pkg_resources.tests.test_pkg_resources (top-level), pkg_resources.tests.test_resources (top-level), pkg_resources.tests.test_working_set (top-level), fsspec.conftest (top-level), trio.testing._raises_group (conditional, optional)
missing module named 'nspkg.subpkg' - imported by pkg_resources.tests.test_resources (delayed)
missing module named nspkg - imported by pkg_resources.tests.test_resources (delayed)
missing module named 'pkg1.pkg2' - imported by pkg_resources.tests.test_resources (delayed)
missing module named pkg1 - imported by pkg_resources.tests.test_resources (delayed)
missing module named mod2 - imported by pkg_resources.tests.test_pkg_resources (delayed)
missing module named mod - imported by pkg_resources.tests.test_pkg_resources (delayed)
missing module named 'jaraco.path' - imported by pkg_resources.tests.test_integration_zope_interface (top-level)
excluded module named __main__ - imported by pkg_resources (delayed, optional)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named jnius - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named android - imported by setuptools._vendor.platformdirs.android (delayed, conditional, optional)
missing module named bcrypt - imported by cryptography.hazmat.primitives.serialization.ssh (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named cryptography.x509.UnsupportedExtension - imported by cryptography.x509 (optional), urllib3.contrib.pyopenssl (optional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level), trio._dtls (delayed, conditional)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level), fsspec.implementations.http_sync (delayed, optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named 'defusedxml.ElementTree' - imported by openpyxl.xml.functions (conditional)
missing module named htmlentitydefs - imported by lxml.html.soupparser (optional)
missing module named BeautifulSoup - imported by lxml.html.soupparser (optional)
missing module named chardet - imported by bs4.dammit (optional), requests.compat (optional), requests (optional), requests.packages (optional), pygments.lexer (delayed, conditional, optional)
missing module named cchardet - imported by bs4.dammit (optional)
missing module named html5lib.treebuilders._base - imported by html5lib.treebuilders (top-level), lxml.html._html5builder (top-level)
missing module named collections.MutableMapping - imported by collections (optional), html5lib.treebuilders.dom (optional), html5lib.treebuilders.etree_lxml (optional)
missing module named html5lib.XHTMLParser - imported by html5lib (optional), lxml.html.html5parser (optional)
missing module named 'genshi.core' - imported by html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by html5lib.treewalkers.genshi (top-level)
missing module named 'chardet.universaldetector' - imported by html5lib._inputstream (delayed, conditional, optional)
missing module named urlparse - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named urllib2 - imported by lxml.ElementInclude (optional), lxml.html.html5parser (optional)
missing module named lxml_html_clean - imported by lxml.html.clean (optional)
missing module named cgi - imported by lxml.doctestcompare (optional)
missing module named cssselect - imported by lxml.cssselect (optional)
missing module named openpyxl.tests - imported by openpyxl.reader.excel (optional)
missing module named simplejson - imported by requests.compat (conditional, optional)
missing module named dummy_threading - imported by requests.cookies (optional)
missing module named win_inet_pton - imported by socks (conditional, optional)
missing module named collections.Callable - imported by collections (optional), socks (optional), cffi.api (optional)
missing module named collections.ValuesView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.KeysView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.ItemsView - imported by collections (optional), sortedcontainers.sorteddict (optional)
missing module named collections.Set - imported by collections (optional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSet - imported by collections (optional), sortedcontainers.sortedset (optional)
missing module named collections.MutableSequence - imported by collections (optional), sortedcontainers.sortedlist (optional)
missing module named collections.Sequence - imported by collections (optional), sortedcontainers.sortedlist (optional), sortedcontainers.sortedset (optional), sortedcontainers.sorteddict (optional)
missing module named numexpr - imported by pandas.core.computation.expressions (conditional), pandas.core.computation.engines (delayed)
missing module named pandas.core.groupby.PanelGroupBy - imported by pandas.core.groupby (delayed, optional), tqdm.std (delayed, optional)
missing module named numba - imported by pandas.core._numba.executor (delayed, conditional), pandas.core.util.numba_ (delayed, conditional), pandas.core.window.numba_ (delayed, conditional), pandas.core.window.online (delayed, conditional), pandas.core._numba.kernels.mean_ (top-level), pandas.core._numba.kernels.shared (top-level), pandas.core._numba.kernels.sum_ (top-level), pandas.core._numba.kernels.min_max_ (top-level), pandas.core._numba.kernels.var_ (top-level), pandas.core.groupby.numba_ (delayed, conditional), pandas.core._numba.extensions (top-level)
missing module named 'numba.extending' - imported by pandas.core._numba.kernels.sum_ (top-level)
missing module named pandas.core.window._Rolling_and_Expanding - imported by pandas.core.window (delayed, optional), tqdm.std (delayed, optional)
missing module named 'pyarrow.compute' - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.arrow.array (conditional)
missing module named 'numba.typed' - imported by pandas.core._numba.extensions (delayed)
missing module named 'numba.core' - imported by pandas.core._numba.extensions (top-level)
missing module named pyarrow - imported by pandas.core.arrays.arrow.accessors (conditional), pandas.core.arrays.masked (delayed), pandas.core.arrays.boolean (delayed, conditional), pandas.core.arrays.string_ (delayed, conditional), pandas.core.arrays._arrow_string_mixins (conditional), pandas.core.arrays.string_arrow (conditional), pandas.core.arrays.arrow._arrow_utils (top-level), pandas.core.interchange.utils (delayed, conditional), pandas.core.strings.accessor (delayed, conditional), pandas.io.parsers.base_parser (delayed, conditional), pandas.core.arrays.interval (delayed), pandas.core.arrays.arrow.extension_types (top-level), pandas.core.arrays.period (delayed), pandas.core.methods.describe (delayed, conditional), pandas.io.sql (delayed, conditional), pandas.core.reshape.merge (delayed, conditional), pandas.core.arrays.numeric (delayed, conditional), pandas.core.interchange.buffer (conditional), pandas.io.feather_format (delayed), pandas.core.indexes.base (delayed, conditional), pandas.core.dtypes.cast (delayed, conditional), pandas.core.arrays.arrow.array (conditional), pandas.core.dtypes.dtypes (delayed, conditional), pandas.compat.pyarrow (optional), pandas.core.reshape.encoding (delayed, conditional), pandas._testing (conditional)
missing module named 'scipy.stats' - imported by pandas.core.nanops (delayed, conditional), pandas.plotting._matplotlib.misc (delayed, conditional), pandas.plotting._matplotlib.hist (delayed)
excluded module named scipy - imported by pandas.core.missing (delayed)
missing module named traitlets - imported by pandas.io.formats.printing (delayed, conditional)
missing module named 'IPython.core' - imported by pandas.io.formats.printing (delayed, conditional), rich.pretty (delayed, optional)
excluded module named IPython - imported by pandas.io.formats.printing (delayed)
missing module named 'odf.config' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.style' - imported by pandas.io.excel._odswriter (delayed)
missing module named 'odf.text' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.table' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named 'odf.opendocument' - imported by pandas.io.excel._odfreader (delayed), pandas.io.excel._odswriter (delayed)
missing module named cStringIO - imported by xlrd.timemachine (conditional), cffi.ffiplatform (optional)
missing module named pyxlsb - imported by pandas.io.excel._pyxlsb (delayed, conditional)
missing module named 'odf.office' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.element' - imported by pandas.io.excel._odfreader (delayed)
missing module named 'odf.namespaces' - imported by pandas.io.excel._odfreader (delayed)
missing module named odf - imported by pandas.io.excel._odfreader (conditional)
missing module named python_calamine - imported by pandas.io.excel._calamine (delayed, conditional)
missing module named 'matplotlib.pyplot' - imported by pandas.io.formats.style (optional), tqdm.gui (delayed), pandas.plotting._matplotlib.tools (delayed), pandas.plotting._matplotlib.style (delayed), pandas.plotting._matplotlib.misc (delayed), pandas.plotting._matplotlib.core (delayed), pandas.plotting._matplotlib.boxplot (delayed), pandas.plotting._matplotlib.hist (delayed), pandas.plotting._matplotlib (delayed)
excluded module named matplotlib - imported by pandas.io.formats.style (optional), tqdm.gui (delayed), pandas.plotting._matplotlib.core (top-level), pandas.plotting._matplotlib.tools (top-level), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.timeseries (delayed)
missing module named 'matplotlib.colors' - imported by pandas.plotting._misc (conditional), pandas.io.formats.style (conditional), pandas.plotting._matplotlib.style (top-level), pandas.plotting._matplotlib.core (delayed)
missing module named botocore - imported by pandas.io.common (delayed, conditional, optional)
missing module named 'scipy.sparse' - imported by pandas.core.arrays.sparse.array (conditional), pandas.core.arrays.sparse.scipy_sparse (delayed, conditional), pandas.core.arrays.sparse.accessor (delayed), pandas.core.dtypes.common (delayed, conditional, optional)
missing module named pandas.core.internals.Block - imported by pandas.core.internals (conditional), pandas.io.pytables (conditional)
missing module named Foundation - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named AppKit - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named PyQt4 - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named qtpy - imported by pandas.io.clipboard (delayed, conditional, optional)
missing module named pysqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed)
missing module named sqlcipher3 - imported by sqlalchemy.dialects.sqlite.pysqlcipher (delayed, optional)
missing module named psycopg2 - imported by sqlalchemy.dialects.postgresql.psycopg2 (delayed)
missing module named 'psycopg.pq' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed)
missing module named 'psycopg.types' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named 'psycopg.adapt' - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named psycopg - imported by sqlalchemy.dialects.postgresql.psycopg (delayed, conditional)
missing module named asyncpg - imported by sqlalchemy.dialects.postgresql.asyncpg (delayed)
missing module named oracledb - imported by sqlalchemy.dialects.oracle.oracledb (delayed, conditional)
missing module named cx_Oracle - imported by sqlalchemy.dialects.oracle.cx_oracle (delayed)
missing module named 'opentelemetry.semconv' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'opentelemetry.sdk' - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named opentelemetry - imported by mysql.connector.opentelemetry.constants (optional), mysql.connector.opentelemetry.context_propagation (conditional), mysql.connector.opentelemetry.instrumentation (optional)
missing module named 'opentelemetry.trace' - imported by mysql.connector.opentelemetry.context_propagation (conditional)
missing module named wmi - imported by dns.win32util (conditional)
missing module named apport_python_hook - imported by exceptiongroup._formatting (conditional)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named _pytest - imported by trio.testing._raises_group (conditional)
missing module named 'setuptools._distutils.msvc9compiler' - imported by cffi._shimmed_dist_utils (conditional, optional)
missing module named imp - imported by cffi.verifier (conditional), cffi._imp_emulation (optional)
missing module named dummy_thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional)
missing module named thread - imported by sortedcontainers.sortedlist (conditional, optional), cffi.lock (conditional, optional), cffi.cparser (conditional, optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named 'hypothesis.internal' - imported by trio._core._run (delayed, optional)
missing module named hypothesis - imported by trio._core._run (delayed)
missing module named '_pytest.outcomes' - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by aiohttp.worker (delayed), anyio._backends._asyncio (delayed, conditional)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.config' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional), rich.jupyter (delayed, optional), rich.live (delayed, conditional, optional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named _winreg - imported by pygments.formatters.img (optional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), rich.live (delayed, conditional, optional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional)
missing module named 'aioquic.quic' - imported by dns.quic._asyncio (top-level), dns.quic._common (top-level), dns.quic._sync (top-level), dns.quic._trio (top-level)
missing module named 'aioquic.h3' - imported by dns.quic._common (top-level)
missing module named aioquic - imported by dns.quic (conditional)
missing module named asyncmy - imported by sqlalchemy.dialects.mysql.asyncmy (delayed)
missing module named nacl - imported by pymysql._auth (delayed, optional)
missing module named tables - imported by pandas.io.pytables (delayed, conditional)
missing module named 'pyarrow.fs' - imported by pandas.io.orc (conditional), fsspec.implementations.arrow (delayed)
missing module named distributed - imported by fsspec.transaction (delayed)
missing module named 'pyarrow.parquet' - imported by pandas.io.parquet (delayed), fsspec.parquet (delayed)
missing module named fastparquet - imported by fsspec.parquet (delayed)
missing module named requests_kerberos - imported by fsspec.implementations.webhdfs (delayed, conditional)
missing module named smbprotocol - imported by fsspec.implementations.smb (top-level)
missing module named smbclient - imported by fsspec.implementations.smb (top-level)
missing module named paramiko - imported by fsspec.implementations.sftp (top-level)
missing module named kerchunk - imported by fsspec.implementations.reference (delayed)
missing module named ujson - imported by fsspec.implementations.cache_metadata (optional), fsspec.implementations.reference (optional)
missing module named 'libarchive.ffi' - imported by fsspec.implementations.libarchive (top-level)
missing module named libarchive - imported by fsspec.implementations.libarchive (top-level)
missing module named async_timeout - imported by aiohttp.helpers (conditional), aiohttp.web_ws (conditional), aiohttp.client_ws (conditional)
missing module named 'gunicorn.workers' - imported by aiohttp.worker (top-level)
missing module named gunicorn - imported by aiohttp.worker (top-level)
missing module named aiodns - imported by aiohttp.resolver (optional)
missing module named pygit2 - imported by fsspec.implementations.git (top-level)
missing module named 'distributed.worker' - imported by fsspec.implementations.dask (top-level)
missing module named 'distributed.client' - imported by fsspec.implementations.dask (top-level)
missing module named dask - imported by fsspec.implementations.dask (top-level)
missing module named panel - imported by fsspec.gui (top-level)
missing module named fuse - imported by fsspec.fuse (top-level)
missing module named lz4 - imported by fsspec.compression (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named setuptools_scm - imported by tqdm.version (optional)
missing module named 'google.auth' - imported by pandas.io.gbq (conditional)
missing module named 'matplotlib.axes' - imported by pandas.plotting._core (conditional), pandas.plotting._misc (conditional), pandas._testing.asserters (delayed), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.timeseries (conditional), pandas.plotting._matplotlib.core (delayed, conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named 'matplotlib.artist' - imported by pandas._testing.asserters (delayed), pandas.plotting._matplotlib.boxplot (top-level), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.figure' - imported by pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (conditional), pandas.plotting._matplotlib.core (conditional), pandas.plotting._matplotlib.boxplot (conditional), pandas.plotting._matplotlib.hist (conditional)
missing module named 'matplotlib.lines' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.misc (top-level), pandas.plotting._matplotlib.boxplot (conditional)
missing module named 'matplotlib.ticker' - imported by pandas.plotting._matplotlib.converter (top-level), pandas.plotting._matplotlib.core (delayed)
missing module named 'matplotlib.axis' - imported by pandas.plotting._matplotlib.tools (conditional), pandas.plotting._matplotlib.converter (conditional), pandas.plotting._matplotlib.core (conditional)
missing module named 'matplotlib.units' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.transforms' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.dates' - imported by pandas.plotting._matplotlib.converter (top-level)
missing module named 'matplotlib.table' - imported by pandas.plotting._misc (conditional), pandas.plotting._matplotlib.tools (top-level)
missing module named pandas.Panel - imported by pandas (delayed, optional), tqdm.std (delayed, optional)
