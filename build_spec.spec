# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['purchase_checker_simplified.py'],
    pathex=[],
    binaries=[],
    datas=[
        # 不在这里包含数据文件，避免冲突
        # config.json将在构建后单独复制
    ],
    hiddenimports=[
        # 核心依赖
        'pandas',
        'requests',
        'openpyxl',
        'urllib3',
        'dingtalk_sheet_utils',

        # pkg_resources相关
        'pkg_resources',
        'pkg_resources.py2_warn',
        'pkg_resources._vendor',
        'pkg_resources._vendor.packaging',
        'pkg_resources._vendor.packaging.version',
        'pkg_resources._vendor.packaging.specifiers',
        'pkg_resources._vendor.packaging.requirements',
        'pkg_resources.extern',

        # importlib相关
        'importlib_metadata',
        'importlib_metadata._adapters',
        'importlib_metadata._meta',
        'importlib_metadata._text',
        'importlib.metadata',
        'importlib.util',

        # jaraco相关（解决jaraco.text错误）
        'jaraco',
        'jaraco.text',
        'jaraco.functools',
        'jaraco.context',
        'jaraco.classes',

        # zipp相关
        'zipp',

        # pandas依赖
        'numpy',
        'pytz',
        'dateutil',
        'dateutil.parser',
        'dateutil.tz',

        # requests依赖
        'urllib3.util',
        'urllib3.util.retry',
        'urllib3.util.connection',
        'urllib3.exceptions',
        'certifi',
        'charset_normalizer',
        'idna',

        # openpyxl依赖
        'et_xmlfile',
        'defusedxml',
        'defusedxml.ElementTree',

        # 系统相关
        'encodings',
        'encodings.utf_8',
        'encodings.cp1252',
        'encodings.latin_1',

        # 其他可能需要的模块
        'calendar',
        'argparse',
        'json',
        'os',
        'sys',
        'datetime',
        'typing',
        'pathlib',
        're',
        'traceback',
        'glob',
        'shutil',
        'subprocess',
    ],
    hookspath=['hooks'],  # 添加自定义hooks目录
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 只排除明确不需要的大型库，减少排除项避免误删
        'matplotlib',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
        'tkinter',
        'turtle',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='采购绩效数据同步',
    debug=False,  # 关闭调试模式，减小文件大小
    bootloader_ignore_signals=False,
    strip=False,  # 不剥离符号，保持完整性
    upx=False,    # 禁用UPX压缩，避免兼容性问题
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
