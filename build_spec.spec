# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['purchase_checker_simplified.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'pandas',
        'requests',
        'openpyxl',
        'urllib3',
        'json',
        'datetime',
        'calendar',
        'argparse',
        'typing',
        'pandas._libs.tslibs.timedeltas',
        'pandas._libs.tslibs.np_datetime',
        'pandas._libs.tslibs.nattype',
        'pandas._libs.properties',
        'pandas.io.excel._openpyxl',
        'openpyxl.cell.cell',
        'openpyxl.workbook',
        'openpyxl.worksheet.worksheet',
        'dingtalk_sheet_utils',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除大型可选库
        'matplotlib',
        'scipy',
        'numpy.distutils',
        'numpy.f2py',
        'numpy.testing',
        'pandas.plotting',
        'pandas.tests',
        'pandas.io.clipboard',
        'pandas.io.html',
        'pandas.io.sql',
        'pandas.io.parquet',
        'pandas.io.feather',
        'xlwt',
        'xlsxwriter',
        'IPython',
        'jupyter',
        'notebook',
        'tkinter',
        'turtle',
        'pydoc',
        'doctest',
        'unittest',
        'test',
        'tests',
        'pytest',
        'coverage',
        'setuptools',
        'pip',
        'wheel',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='采购绩效数据同步',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
